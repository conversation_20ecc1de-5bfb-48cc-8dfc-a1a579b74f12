{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 3810753131390094618, "deps": [[4450062412064442726, "dirs_next", false, 18190101891643227920], [4899080583175475170, "semver", false, 5026173323402683378], [7468248713591957673, "cargo_toml", false, 2931738064806493165], [8292277814562636972, "tauri_utils", false, 967489192104631831], [8569119365930580996, "serde_json", false, 1091898351837220986], [9689903380558560274, "serde", false, 18184860513468081355], [10301936376833819828, "json_patch", false, 2347138052917102529], [13077543566650298139, "heck", false, 17114546617626299270], [13625485746686963219, "anyhow", false, 7630964361272369738], [14189313126492979171, "tauri_winres", false, 11159056484958477465], [15622660310229662834, "walkdir", false, 9948530080212857216]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-309617dd76d9e913\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}