<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>提肛小助手 API 测试</h1>
    
    <div class="test-section">
        <h3>1. 健康检查</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 获取设置</h3>
        <button onclick="testGetSettings()">获取用户设置</button>
        <div id="settings-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 开始运动</h3>
        <button onclick="testStartExercise()">开始运动</button>
        <div id="start-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 完成运动</h3>
        <button onclick="testCompleteExercise()">完成运动</button>
        <div id="complete-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. 获取统计</h3>
        <button onclick="testGetStats()">获取统计数据</button>
        <div id="stats-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/v1';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.textContent = `✅ 成功 (${result.status})\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ 失败\n${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }
        
        async function testHealth() {
            const result = await makeRequest(`${API_BASE}/../health`);
            displayResult('health-result', result);
        }
        
        async function testGetSettings() {
            const result = await makeRequest(`${API_BASE}/settings`);
            displayResult('settings-result', result);
        }
        
        async function testStartExercise() {
            const result = await makeRequest(`${API_BASE}/exercise/start`, {
                method: 'POST'
            });
            displayResult('start-result', result);
        }
        
        async function testCompleteExercise() {
            const result = await makeRequest(`${API_BASE}/exercise/complete`, {
                method: 'POST'
            });
            displayResult('complete-result', result);
        }
        
        async function testGetStats() {
            const result = await makeRequest(`${API_BASE}/stats`);
            displayResult('stats-result', result);
        }
        
        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
