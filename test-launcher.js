#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const http = require('http');
const fs = require('fs');
const express = require('express');

console.log('\n╔══════════════════════════════════════╗');
console.log('║        提肛小助手测试启动器            ║');
console.log('╚══════════════════════════════════════╝\n');

const BACKEND_PORT = 8000;
const FRONTEND_PORT = 8080;
const BACKEND_URL = `http://127.0.0.1:${BACKEND_PORT}`;
const FRONTEND_URL = `http://127.0.0.1:${FRONTEND_PORT}`;

let backendProcess = null;
let frontendServer = null;

// 检查端口是否被占用
function checkPort(port) {
    return new Promise((resolve) => {
        const server = http.createServer();
        server.listen(port, '127.0.0.1', () => {
            server.close(() => resolve(true));
        });
        server.on('error', () => resolve(false));
    });
}

// 等待服务启动
function waitForService(url, maxAttempts = 30) {
    return new Promise((resolve) => {
        let attempts = 0;
        const check = () => {
            attempts++;
            http.get(url + '/health', (res) => {
                if (res.statusCode === 200) {
                    resolve(true);
                } else if (attempts < maxAttempts) {
                    setTimeout(check, 1000);
                } else {
                    resolve(false);
                }
            }).on('error', () => {
                if (attempts < maxAttempts) {
                    setTimeout(check, 1000);
                } else {
                    resolve(false);
                }
            });
        };
        check();
    });
}

// 启动后端服务
async function startBackend() {
    console.log('[1/3] 启动后端API服务...');
    
    const backendPath = path.join(__dirname, 'backend');
    const isPortFree = await checkPort(BACKEND_PORT);
    
    if (!isPortFree) {
        console.log(`⚠️  端口 ${BACKEND_PORT} 已被占用，尝试连接现有服务...`);
        const isRunning = await waitForService(BACKEND_URL, 3);
        if (isRunning) {
            console.log('✅ 后端服务已在运行');
            return true;
        } else {
            console.log('❌ 端口被占用但服务不可用');
            return false;
        }
    }
    
    backendProcess = spawn('python', ['start.py'], {
        cwd: backendPath,
        stdio: ['ignore', 'pipe', 'pipe']
    });
    
    backendProcess.stdout.on('data', (data) => {
        console.log(`后端: ${data.toString().trim()}`);
    });
    
    backendProcess.stderr.on('data', (data) => {
        console.error(`后端错误: ${data.toString().trim()}`);
    });
    
    console.log('[2/3] 等待后端服务启动...');
    const isStarted = await waitForService(BACKEND_URL);
    
    if (isStarted) {
        console.log('✅ 后端服务启动成功');
        return true;
    } else {
        console.log('❌ 后端服务启动失败');
        return false;
    }
}

// 启动前端服务
async function startFrontend() {
    console.log('[3/3] 启动前端Web应用...');
    
    const frontendDistPath = path.join(__dirname, 'frontend', 'dist');
    
    if (!fs.existsSync(frontendDistPath)) {
        console.log('❌ 前端构建文件不存在，请先运行构建');
        return false;
    }
    
    const isPortFree = await checkPort(FRONTEND_PORT);
    if (!isPortFree) {
        console.log(`⚠️  端口 ${FRONTEND_PORT} 已被占用，使用端口 ${FRONTEND_PORT + 1}`);
    }
    
    // 使用Express静态文件服务器
    const app = express();
    
    // 配置静态文件服务
    app.use(express.static(frontendDistPath));
    
    // 配置API代理
    app.use('/api', (req, res) => {
        const targetUrl = `${BACKEND_URL}${req.url}`;
        console.log(`代理请求: ${req.method} ${req.url} -> ${targetUrl}`);
        
        // 简单的代理实现
        const options = {
            hostname: '127.0.0.1',
            port: BACKEND_PORT,
            path: req.url,
            method: req.method,
            headers: req.headers
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });
        
        proxyReq.on('error', (err) => {
            console.error('代理错误:', err);
            res.status(500).json({ error: '代理请求失败' });
        });
        
        req.pipe(proxyReq);
    });
    
    // 配置SPA路由
    app.get('*', (req, res) => {
        res.sendFile(path.join(frontendDistPath, 'index.html'));
    });
    
    const actualPort = isPortFree ? FRONTEND_PORT : FRONTEND_PORT + 1;
    frontendServer = app.listen(actualPort, '127.0.0.1', () => {
        console.log(`✅ 前端服务启动成功: http://127.0.0.1:${actualPort}`);
        
        // 自动打开浏览器
        setTimeout(() => {
            console.log('\n🚀 正在打开浏览器...');
            const { exec } = require('child_process');
            exec(`start http://127.0.0.1:${actualPort}`);
        }, 1000);
    });
    
    return frontendServer;
}

// 优雅关闭
function gracefulShutdown() {
    console.log('\n🛑 正在关闭服务...');
    
    if (backendProcess) {
        backendProcess.kill();
        console.log('✅ 后端服务已关闭');
    }
    
    if (frontendServer) {
        frontendServer.close();
        console.log('✅ 前端服务已关闭');
    }
    
    console.log('👋 再见！');
    process.exit(0);
}

// 主函数
async function main() {
    try {
        // 启动后端
        const backendStarted = await startBackend();
        if (!backendStarted) {
            console.log('❌ 后端启动失败，退出');
            process.exit(1);
        }
        
        // 启动前端
        const frontend = await startFrontend();
        if (!frontend) {
            console.log('❌ 前端启动失败，退出');
            process.exit(1);
        }
        
        console.log('\n🎉 提肛小助手启动成功！');
        console.log(`📱 前端地址: http://127.0.0.1:${FRONTEND_PORT}`);
        console.log(`🔧 后端地址: ${BACKEND_URL}`);
        console.log('\n按 Ctrl+C 退出应用\n');
        
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    }
}

// 监听退出信号
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);
process.on('exit', gracefulShutdown);

// 启动应用
main();
