import axios from 'axios'
import type { UserSettings, ExerciseStats, ReminderStatus, ApiResponse } from '@/types'

// 检测运行环境并设置API基础URL
const getApiBaseURL = () => {
  // 检测是否在Tauri环境中
  if (window.__TAURI__) {
    return 'http://127.0.0.1:8000/api/v1'
  }
  // 开发环境或其他环境使用相对路径
  return '/api/v1'
}

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      // 服务器返回错误状态码
      const message = error.response.data?.message || '服务器错误'
      return Promise.reject(new Error(message))
    } else if (error.request) {
      // 请求发送失败
      return Promise.reject(new Error('网络连接失败'))
    } else {
      // 其他错误
      return Promise.reject(new Error('请求配置错误'))
    }
  }
)

// 设置相关API
export const getSettings = (): Promise<ApiResponse<UserSettings>> => {
  return api.get('/settings')
}

export const updateSettings = (settings: UserSettings): Promise<ApiResponse> => {
  return api.post('/settings', settings)
}

// 统计数据相关API
export const getStats = (): Promise<ApiResponse<ExerciseStats>> => {
  return api.get('/stats')
}

// 运动相关API
export const startExercise = (): Promise<ApiResponse> => {
  return api.post('/exercise/start')
}

export const completeExercise = (): Promise<ApiResponse> => {
  return api.post('/exercise/complete')
}

// 提醒相关API
export const getReminderStatus = (): Promise<ApiResponse<ReminderStatus>> => {
  return api.get('/reminder/status')
}

export const toggleReminder = (): Promise<ApiResponse> => {
  return api.post('/reminder/toggle')
}

// 健康检查
export const healthCheck = (): Promise<ApiResponse> => {
  return api.get('/health')
}

export default api
