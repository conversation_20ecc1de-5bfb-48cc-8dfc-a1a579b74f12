// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{
    Manager, Window, WindowEvent, GlobalShortcutManager, AppHandle, State
};
use std::sync::Mutex;
use std::process::{Command, Child};
use chrono::{DateTime, Local};
use serde::{Deserialize, Serialize};

// 应用状态
#[derive(Default)]
struct AppState {
    reminder_enabled: Mutex<bool>,
    last_reminder: Mutex<Option<DateTime<Local>>>,
    backend_process: Mutex<Option<Child>>,
}

// 设置结构
#[derive(Serialize, Deserialize, Debug)]
struct Settings {
    reminder_interval: u32,
    exercise_duration: u32,
    repetitions: u32,
    enable_notifications: bool,
    enable_sound: bool,
    working_hours_enabled: bool,
    working_hours_start: String,
    working_hours_end: String,
}

impl Default for Settings {
    fn default() -> Self {
        Self {
            reminder_interval: 30,
            exercise_duration: 5,
            repetitions: 10,
            enable_notifications: true,
            enable_sound: true,
            working_hours_enabled: true,
            working_hours_start: "09:00".to_string(),
            working_hours_end: "18:00".to_string(),
        }
    }
}

// Tauri 命令
#[tauri::command]
async fn show_notification(title: String, body: String) -> Result<(), String> {
    tauri::api::notification::Notification::new("com.kegelhelper.app")
        .title(&title)
        .body(&body)
        .show()
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn toggle_reminder(state: State<'_, AppState>) -> Result<bool, String> {
    let mut enabled = state.reminder_enabled.lock().unwrap();
    *enabled = !*enabled;
    Ok(*enabled)
}

#[tauri::command]
async fn get_reminder_status(state: State<'_, AppState>) -> Result<bool, String> {
    let enabled = state.reminder_enabled.lock().unwrap();
    Ok(*enabled)
}

#[tauri::command]
async fn minimize_to_tray(window: Window) -> Result<(), String> {
    window.hide().map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn show_from_tray(window: Window) -> Result<(), String> {
    window.show().map_err(|e| e.to_string())?;
    window.set_focus().map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn register_global_shortcut(app: AppHandle) -> Result<(), String> {
    let mut shortcut_manager = app.global_shortcut_manager();
    
    // 注册快捷键 Ctrl+Shift+K 来显示/隐藏窗口
    shortcut_manager
        .register("CmdOrCtrl+Shift+K", move || {
            if let Some(window) = app.get_window("main") {
                if window.is_visible().unwrap_or(false) {
                    let _ = window.hide();
                } else {
                    let _ = window.show();
                    let _ = window.set_focus();
                }
            }
        })
        .map_err(|e| e.to_string())?;
    
    Ok(())
}

#[tauri::command]
async fn send_reminder_notification() -> Result<(), String> {
    show_notification(
        "提肛小助手".to_string(),
        "该做提肛运动了！保持健康从现在开始 💪".to_string(),
    ).await
}

#[tauri::command]
async fn start_backend_service(state: State<'_, AppState>) -> Result<String, String> {
    let mut backend_process = state.backend_process.lock().unwrap();

    // 如果后端已经在运行，直接返回
    if backend_process.is_some() {
        return Ok("后端服务已在运行".to_string());
    }

    // 获取应用资源目录
    let backend_path = std::env::current_exe()
        .map_err(|e| format!("获取应用路径失败: {}", e))?
        .parent()
        .ok_or("获取父目录失败")?
        .join("backend");

    // 启动Python后端服务
    let child = Command::new("python")
        .arg("start.py")
        .current_dir(&backend_path)
        .spawn()
        .map_err(|e| format!("启动后端服务失败: {}", e))?;

    *backend_process = Some(child);
    Ok("后端服务启动成功".to_string())
}

#[tauri::command]
async fn stop_backend_service(state: State<'_, AppState>) -> Result<String, String> {
    let mut backend_process = state.backend_process.lock().unwrap();

    if let Some(mut child) = backend_process.take() {
        child.kill().map_err(|e| format!("停止后端服务失败: {}", e))?;
        Ok("后端服务已停止".to_string())
    } else {
        Ok("后端服务未运行".to_string())
    }
}

// System tray functionality temporarily disabled for compatibility

fn main() {
    tauri::Builder::default()
        .manage(AppState::default())

        .on_window_event(|event| match event.event() {
            WindowEvent::CloseRequested { api, .. } => {
                // 阻止窗口关闭，改为隐藏到系统托盘
                event.window().hide().unwrap();
                api.prevent_close();
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            show_notification,
            toggle_reminder,
            get_reminder_status,
            minimize_to_tray,
            show_from_tray,
            register_global_shortcut,
            send_reminder_notification,
            start_backend_service,
            stop_backend_service
        ])
        .setup(|app| {
            // 注册全局快捷键
            let app_handle = app.handle();
            tauri::async_runtime::spawn(async move {
                let _ = register_global_shortcut(app_handle).await;
            });

            // 启动后端服务
            let state = app.state::<AppState>();
            tauri::async_runtime::spawn(async move {
                let _ = start_backend_service(state).await;
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
