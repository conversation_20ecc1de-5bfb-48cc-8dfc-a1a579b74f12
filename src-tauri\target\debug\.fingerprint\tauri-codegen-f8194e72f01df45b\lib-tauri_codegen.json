{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 5866681239136900042, "deps": [[3060637413840920116, "proc_macro2", false, 8406240879912885391], [4899080583175475170, "semver", false, 5026173323402683378], [7392050791754369441, "ico", false, 8639281339023745352], [8008191657135824715, "thiserror", false, 11176856808456277608], [8292277814562636972, "tauri_utils", false, 967489192104631831], [8319709847752024821, "uuid", false, 6814420306548153876], [8569119365930580996, "serde_json", false, 1091898351837220986], [9451456094439810778, "regex", false, 9285598514362218264], [9689903380558560274, "serde", false, 18184860513468081355], [9857275760291862238, "sha2", false, 4792154269115683185], [10301936376833819828, "json_patch", false, 2347138052917102529], [12687914511023397207, "png", false, 14370224349459338303], [14132538657330703225, "brotli", false, 639198843148406430], [15622660310229662834, "walkdir", false, 9948530080212857216], [17990358020177143287, "quote", false, 7647860114736051552], [18066890886671768183, "base64", false, 18195296184290163715]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-f8194e72f01df45b\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}