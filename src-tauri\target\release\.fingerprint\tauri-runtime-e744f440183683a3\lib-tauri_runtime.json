{"rustc": 1842507548689473721, "features": "[\"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 16287072401622553093, "path": 1339320275069559873, "deps": [[3150220818285335163, "url", false, 2607936323075669519], [4381063397040571828, "webview2_com", false, 2109139913616277398], [4405182208873388884, "http", false, 8119963941407273293], [7653476968652377684, "windows", false, 2672332482407547069], [8008191657135824715, "thiserror", false, 7441644336200258125], [8292277814562636972, "tauri_utils", false, 16259485879196958486], [8319709847752024821, "uuid", false, 7522972156232273135], [8569119365930580996, "serde_json", false, 11246661942346795939], [8866577183823226611, "http_range", false, 15781284202079710687], [9689903380558560274, "serde", false, 16541609886822019218], [11693073011723388840, "raw_window_handle", false, 3518983977318704076], [13208667028893622512, "rand", false, 5082811502463348362], [14162324460024849578, "build_script_build", false, 5544575813342655763]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-e744f440183683a3\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}