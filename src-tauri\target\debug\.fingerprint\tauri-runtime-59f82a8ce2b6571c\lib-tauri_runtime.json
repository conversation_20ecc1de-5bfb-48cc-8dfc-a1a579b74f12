{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 1339320275069559873, "deps": [[3150220818285335163, "url", false, 4649323851504239578], [4381063397040571828, "webview2_com", false, 1518854012022457691], [4405182208873388884, "http", false, 8709409067720687175], [7653476968652377684, "windows", false, 11044908766836782643], [8008191657135824715, "thiserror", false, 11176856808456277608], [8292277814562636972, "tauri_utils", false, 2740083403207549112], [8319709847752024821, "uuid", false, 6814420306548153876], [8569119365930580996, "serde_json", false, 6500556973627130142], [8866577183823226611, "http_range", false, 13550116262776823979], [9689903380558560274, "serde", false, 18184860513468081355], [11693073011723388840, "raw_window_handle", false, 14277391599310659604], [13208667028893622512, "rand", false, 7376770358957140781], [14162324460024849578, "build_script_build", false, 16636897965233613229]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-59f82a8ce2b6571c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}