import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { isTauri, startBackendService, waitForBackendService } from './utils/tauri'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 初始化应用
const initApp = async () => {
  // 如果在Tauri环境中，启动后端服务
  if (isTauri()) {
    try {
      console.log('检测到Tauri环境，正在启动后端服务...')
      await startBackendService()

      // 等待后端服务启动
      const isBackendReady = await waitForBackendService()
      if (isBackendReady) {
        console.log('后端服务启动成功')
      } else {
        console.warn('后端服务启动超时，但应用将继续运行')
      }
    } catch (error) {
      console.error('启动后端服务失败:', error)
    }
  }

  // 挂载应用
  app.mount('#app')
}

// 启动应用
initApp()
