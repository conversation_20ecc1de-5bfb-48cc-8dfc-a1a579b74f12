{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 5866681239136900042, "deps": [[3060637413840920116, "proc_macro2", false, 6659999382514378454], [4899080583175475170, "semver", false, 10474756973477593717], [7392050791754369441, "ico", false, 5112158392316718814], [8008191657135824715, "thiserror", false, 9788101540380314465], [8292277814562636972, "tauri_utils", false, 16465916010854080396], [8319709847752024821, "uuid", false, 5896744528637344822], [8569119365930580996, "serde_json", false, 4380246218091041972], [9451456094439810778, "regex", false, 15121228974452032340], [9689903380558560274, "serde", false, 1703231738526593199], [9857275760291862238, "sha2", false, 487848962875048405], [10301936376833819828, "json_patch", false, 13928870146076332136], [12687914511023397207, "png", false, 16815244982628497704], [14132538657330703225, "brotli", false, 9165950019429130629], [15622660310229662834, "walkdir", false, 11231307626965393364], [17990358020177143287, "quote", false, 8260028736195906517], [18066890886671768183, "base64", false, 14063334798095279410]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-c9e58d4d73021697\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}