{"rustc": 1842507548689473721, "features": "[\"bytes\", \"compression\", \"default\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"objc-exception\", \"open\", \"path-all\", \"process-exit\", \"process-relaunch\", \"regex\", \"reqwest\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 2779529947099847214, "deps": [[40386456601120721, "percent_encoding", false, 3322157491714906925], [1260461579271933187, "serialize_to_javascript", false, 3276515908212624477], [1441306149310335789, "tempfile", false, 8388170427682540433], [3150220818285335163, "url", false, 4649323851504239578], [3722963349756955755, "once_cell", false, 6564127573579152074], [3988549704697787137, "open", false, 7769844836913504399], [4381063397040571828, "webview2_com", false, 1518854012022457691], [4405182208873388884, "http", false, 8709409067720687175], [4450062412064442726, "dirs_next", false, 18190101891643227920], [4899080583175475170, "semver", false, 10979775137585629632], [5180608563399064494, "tauri_macros", false, 11706875375237625523], [5610773616282026064, "build_script_build", false, 7072961668095787570], [5986029879202738730, "log", false, 9663051989621911511], [7244058819997729774, "reqwest", false, 1661660432795680005], [7653476968652377684, "windows", false, 11044908766836782643], [8008191657135824715, "thiserror", false, 11176856808456277608], [8292277814562636972, "tauri_utils", false, 2740083403207549112], [8319709847752024821, "uuid", false, 6814420306548153876], [8569119365930580996, "serde_json", false, 6500556973627130142], [9451456094439810778, "regex", false, 10135153710775633150], [9623796893764309825, "ignore", false, 11105174768280785752], [9689903380558560274, "serde", false, 18184860513468081355], [9920160576179037441, "getrandom", false, 15187506789297675995], [10629569228670356391, "futures_util", false, 13472616021501578053], [11601763207901161556, "tar", false, 11685234250534288077], [11693073011723388840, "raw_window_handle", false, 14277391599310659604], [11989259058781683633, "dunce", false, 16815482336677619011], [12944427623413450645, "tokio", false, 986304976615035917], [12986574360607194341, "serde_repr", false, 6493277905820674536], [13208667028893622512, "rand", false, 7376770358957140781], [13625485746686963219, "anyhow", false, 7630964361272369738], [14162324460024849578, "tauri_runtime", false, 16711917615557223865], [14564311161534545801, "encoding_rs", false, 10920306531369071843], [14923790796823607459, "indexmap", false, 17237386747318579649], [16066129441945555748, "bytes", false, 11439261334544637054], [16228250612241359704, "tauri_runtime_wry", false, 10766601787846990645], [17155886227862585100, "glob", false, 13938711830105574337], [17278893514130263345, "state", false, 13170078685296798982], [17772299992546037086, "flate2", false, 5237154560397513850]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-12cddd44f1c57701\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}