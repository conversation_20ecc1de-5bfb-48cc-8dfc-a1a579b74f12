import{_ as N}from"./_plugin-vue_export-helper-BiY_o3Ly.js";/* empty css                  */import{d as P,u as j,r as z,c as U,o as F,a as S,b as t,w as s,E as G,e as y,f as J,g as d,h as K,i as n,j as i,k as L,l as O,m as Q,n as W,t as _,p as c,q as X,s as C}from"./index-C32AESHs.js";import{u as Y}from"./settings-CmLFLaNK.js";import{u as Z}from"./exercise-CGbk4vEv.js";const tt={class:"home-view"},et={class:"header-content"},st={class:"title"},nt={class:"nav-buttons"},ot={class:"status-cards"},at={class:"card-content"},lt={class:"card-info"},dt={class:"card-value"},rt={class:"card-content"},it={class:"card-info"},ct={class:"card-value"},ut={class:"card-content"},_t={class:"card-info"},mt={class:"card-value"},pt={class:"card-header"},ft={class:"reminder-content"},vt={key:0,class:"reminder-info"},gt={key:1,class:"reminder-disabled"},St={class:"quick-start"},yt=P({__name:"HomeView",setup(kt){const h=J(),k=Y(),l=Z(),m=j(),a=z(!1),x=U(()=>{if(!l.reminderStatus.nextReminder)return"未设置";const r=new Date(l.reminderStatus.nextReminder),e=new Date,p=r.getTime()-e.getTime();if(p<=0)return"即将提醒";const o=Math.floor(p/(1e3*60)),u=Math.floor(o/60);return u>0?`${u}小时${o%60}分钟后`:`${o}分钟后`}),E=async()=>{try{if(m.isDesktop){const r=await m.toggleReminder();a.value=r}else a.value=!a.value,C.success(a.value?"提醒已开启":"提醒已关闭")}catch{C.error("切换提醒状态失败"),a.value=!a.value}},b=()=>{h.push("/exercise")};return F(async()=>{await Promise.all([k.loadSettings(),l.loadStats(),l.loadReminderStatus()]),m.isDesktop?a.value=m.reminderEnabled:a.value=l.reminderStatus.enabled}),(r,e)=>{const p=d("Aim"),o=L,u=d("VideoPlay"),f=O,w=d("DataAnalysis"),T=d("Setting"),D=K,V=d("Calendar"),v=W,$=d("Trophy"),B=d("DataBoard"),R=X,M=d("Clock"),A=d("Timer"),q=d("Bell"),H=Q,I=G;return y(),S("div",tt,[t(I,null,{default:s(()=>[t(D,{class:"header"},{default:s(()=>[n("div",et,[n("h1",st,[t(o,null,{default:s(()=>[t(p)]),_:1}),e[4]||(e[4]=i(" 提肛小助手 ",-1))]),n("div",nt,[t(f,{type:"primary",onClick:e[0]||(e[0]=g=>r.$router.push("/exercise"))},{default:s(()=>[t(o,null,{default:s(()=>[t(u)]),_:1}),e[5]||(e[5]=i(" 开始运动 ",-1))]),_:1,__:[5]}),t(f,{onClick:e[1]||(e[1]=g=>r.$router.push("/stats"))},{default:s(()=>[t(o,null,{default:s(()=>[t(w)]),_:1}),e[6]||(e[6]=i(" 统计 ",-1))]),_:1,__:[6]}),t(f,{onClick:e[2]||(e[2]=g=>r.$router.push("/settings"))},{default:s(()=>[t(o,null,{default:s(()=>[t(T)]),_:1}),e[7]||(e[7]=i(" 设置 ",-1))]),_:1,__:[7]})])])]),_:1}),t(H,{class:"main-content"},{default:s(()=>[n("div",ot,[t(v,{class:"status-card"},{default:s(()=>[n("div",at,[t(o,{class:"card-icon today"},{default:s(()=>[t(V)]),_:1}),n("div",lt,[n("div",dt,_(c(l).stats.todayCount),1),e[8]||(e[8]=n("div",{class:"card-label"},"今日次数",-1))])])]),_:1}),t(v,{class:"status-card"},{default:s(()=>[n("div",rt,[t(o,{class:"card-icon streak"},{default:s(()=>[t($)]),_:1}),n("div",it,[n("div",ct,_(c(l).stats.streakDays),1),e[9]||(e[9]=n("div",{class:"card-label"},"连续天数",-1))])])]),_:1}),t(v,{class:"status-card"},{default:s(()=>[n("div",ut,[t(o,{class:"card-icon total"},{default:s(()=>[t(B)]),_:1}),n("div",_t,[n("div",mt,_(c(l).stats.totalCount),1),e[10]||(e[10]=n("div",{class:"card-label"},"总计次数",-1))])])]),_:1})]),t(v,{class:"reminder-card"},{header:s(()=>[n("div",pt,[e[11]||(e[11]=n("span",null,"提醒状态",-1)),t(R,{modelValue:a.value,"onUpdate:modelValue":e[3]||(e[3]=g=>a.value=g),onChange:E,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])])]),default:s(()=>[n("div",ft,[a.value?(y(),S("div",vt,[n("p",null,[t(o,null,{default:s(()=>[t(M)]),_:1}),i(" 下次提醒: "+_(x.value),1)]),n("p",null,[t(o,null,{default:s(()=>[t(A)]),_:1}),i(" 提醒间隔: "+_(c(k).settings.reminderInterval)+" 分钟 ",1)])])):(y(),S("div",gt,[t(o,null,{default:s(()=>[t(q)]),_:1}),e[12]||(e[12]=i(" 提醒已关闭 ",-1))]))])]),_:1}),n("div",St,[t(f,{type:"primary",size:"large",class:"start-button",onClick:b,loading:c(l).loading,disabled:!c(l).canStartExercise},{default:s(()=>[t(o,null,{default:s(()=>[t(u)]),_:1}),e[13]||(e[13]=i(" 快速开始运动 ",-1))]),_:1,__:[13]},8,["loading","disabled"])])]),_:1})]),_:1})])}}}),wt=N(yt,[["__scopeId","data-v-0ed46dcf"]]);export{wt as default};
