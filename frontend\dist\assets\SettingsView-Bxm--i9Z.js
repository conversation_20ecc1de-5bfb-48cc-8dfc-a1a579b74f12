import{_ as z}from"./_plugin-vue_export-helper-B5dvQhNj.js";/* empty css                       *//* empty css                  */import{d as Q,r as w,c as S,v as X,o as Y,a as Z,b as l,w as t,E as h,I as ee,e as x,h as le,i as n,l as te,j as d,k as oe,g as i,p as ne,m as ae,J as se,n as re,G as ue,y as de,K as ie,L as me,q as pe,M as _e,N as fe,O as ve,s as p,f as ge}from"./index-A4YXfFtm.js";import{u as Ve}from"./settings-owzpmluW.js";const we={class:"settings-view"},be={class:"header-content"},ce={class:"card-header"},ke={class:"time-range"},Se={class:"card-header"},ye={class:"card-header"},Ee={class:"card-header"},De={class:"reset-warning"},He=Q({__name:"SettingsView",setup(xe){ge();const m=Ve(),o=w({...m.settings}),f=w(!1),v=w(new Date),g=w(new Date),C=S(()=>s=>`${s}分钟`),B=S(()=>s=>`${s}秒`),U=S(()=>s=>`${s}次`);X([v,g],([s,e])=>{s&&e&&(o.value.workingHours.start=y(s),o.value.workingHours.end=y(e))});const y=s=>`${s.getHours().toString().padStart(2,"0")}:${s.getMinutes().toString().padStart(2,"0")}`,E=s=>{const[e,b]=s.split(":").map(Number),r=new Date;return r.setHours(e,b,0,0),r},T=async()=>{try{await m.saveSettings(o.value)?p.success("设置保存成功"):p.error("设置保存失败")}catch{p.error("保存设置时出错")}},N=async()=>{try{p.info("数据导出功能开发中...")}catch{p.error("导出数据失败")}},$=()=>{f.value=!0},I=async()=>{try{m.resetSettings(),o.value={...m.settings},f.value=!1,p.success("数据重置成功")}catch{p.error("重置数据失败")}};return Y(async()=>{await m.loadSettings(),o.value={...m.settings},v.value=E(o.value.workingHours.start),g.value=E(o.value.workingHours.end)}),(s,e)=>{const b=i("ArrowLeft"),r=oe,_=te,M=le,F=i("Bell"),c=me,u=ie,k=pe,D=_e,V=re,R=i("Trophy"),A=i("Brush"),H=ve,L=fe,G=i("DataBoard"),W=i("Download"),j=i("Delete"),q=se,J=ae,K=h,O=i("WarningFilled"),P=ee;return x(),Z("div",we,[l(K,null,{default:t(()=>[l(M,{class:"header"},{default:t(()=>[n("div",be,[l(_,{onClick:e[0]||(e[0]=a=>s.$router.back()),type:"text",class:"back-btn"},{default:t(()=>[l(r,null,{default:t(()=>[l(b)]),_:1}),e[12]||(e[12]=d(" 返回 ",-1))]),_:1,__:[12]}),e[14]||(e[14]=n("h2",null,"设置",-1)),l(_,{onClick:T,type:"primary",loading:ne(m).loading},{default:t(()=>e[13]||(e[13]=[d(" 保存 ",-1)])),_:1,__:[13]},8,["loading"])])]),_:1}),l(J,{class:"main-content"},{default:t(()=>[l(q,{model:o.value,"label-width":"120px",class:"settings-form"},{default:t(()=>[l(V,{class:"setting-card"},{header:t(()=>[n("div",ce,[l(r,null,{default:t(()=>[l(F)]),_:1}),e[15]||(e[15]=n("span",null,"提醒设置",-1))])]),default:t(()=>[l(u,{label:"提醒间隔"},{default:t(()=>[l(c,{modelValue:o.value.reminderInterval,"onUpdate:modelValue":e[1]||(e[1]=a=>o.value.reminderInterval=a),min:5,max:120,step:5,"show-input":"","format-tooltip":C.value},null,8,["modelValue","format-tooltip"]),e[16]||(e[16]=n("div",{class:"form-help"},"每隔多长时间提醒一次运动（5-120分钟）",-1))]),_:1,__:[16]}),l(u,{label:"工作时间限制"},{default:t(()=>[l(k,{modelValue:o.value.workingHours.enabled,"onUpdate:modelValue":e[2]||(e[2]=a=>o.value.workingHours.enabled=a)},null,8,["modelValue"]),e[17]||(e[17]=n("div",{class:"form-help"},"只在工作时间内发送提醒",-1))]),_:1,__:[17]}),o.value.workingHours.enabled?(x(),ue(u,{key:0,label:"工作时间"},{default:t(()=>[n("div",ke,[l(D,{modelValue:v.value,"onUpdate:modelValue":e[3]||(e[3]=a=>v.value=a),format:"HH:mm",placeholder:"开始时间"},null,8,["modelValue"]),e[18]||(e[18]=n("span",{class:"time-separator"},"至",-1)),l(D,{modelValue:g.value,"onUpdate:modelValue":e[4]||(e[4]=a=>g.value=a),format:"HH:mm",placeholder:"结束时间"},null,8,["modelValue"])])]),_:1})):de("",!0),l(u,{label:"启用通知"},{default:t(()=>[l(k,{modelValue:o.value.enableNotifications,"onUpdate:modelValue":e[5]||(e[5]=a=>o.value.enableNotifications=a)},null,8,["modelValue"]),e[19]||(e[19]=n("div",{class:"form-help"},"显示系统通知",-1))]),_:1,__:[19]}),l(u,{label:"启用音效"},{default:t(()=>[l(k,{modelValue:o.value.enableSound,"onUpdate:modelValue":e[6]||(e[6]=a=>o.value.enableSound=a)},null,8,["modelValue"]),e[20]||(e[20]=n("div",{class:"form-help"},"播放提醒音效",-1))]),_:1,__:[20]})]),_:1}),l(V,{class:"setting-card"},{header:t(()=>[n("div",Se,[l(r,null,{default:t(()=>[l(R)]),_:1}),e[21]||(e[21]=n("span",null,"运动设置",-1))])]),default:t(()=>[l(u,{label:"运动时长"},{default:t(()=>[l(c,{modelValue:o.value.exerciseDuration,"onUpdate:modelValue":e[7]||(e[7]=a=>o.value.exerciseDuration=a),min:3,max:30,"show-input":"","format-tooltip":B.value},null,8,["modelValue","format-tooltip"]),e[22]||(e[22]=n("div",{class:"form-help"},"每次收缩持续时间（3-30秒）",-1))]),_:1,__:[22]}),l(u,{label:"重复次数"},{default:t(()=>[l(c,{modelValue:o.value.repetitions,"onUpdate:modelValue":e[8]||(e[8]=a=>o.value.repetitions=a),min:5,max:50,step:5,"show-input":"","format-tooltip":U.value},null,8,["modelValue","format-tooltip"]),e[23]||(e[23]=n("div",{class:"form-help"},"每次运动的重复次数（5-50次）",-1))]),_:1,__:[23]})]),_:1}),l(V,{class:"setting-card"},{header:t(()=>[n("div",ye,[l(r,null,{default:t(()=>[l(A)]),_:1}),e[24]||(e[24]=n("span",null,"界面设置",-1))])]),default:t(()=>[l(u,{label:"主题"},{default:t(()=>[l(L,{modelValue:o.value.theme,"onUpdate:modelValue":e[9]||(e[9]=a=>o.value.theme=a)},{default:t(()=>[l(H,{label:"light"},{default:t(()=>e[25]||(e[25]=[d("浅色主题",-1)])),_:1,__:[25]}),l(H,{label:"dark"},{default:t(()=>e[26]||(e[26]=[d("深色主题",-1)])),_:1,__:[26]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(V,{class:"setting-card"},{header:t(()=>[n("div",Ee,[l(r,null,{default:t(()=>[l(G)]),_:1}),e[27]||(e[27]=n("span",null,"数据管理",-1))])]),default:t(()=>[l(u,{label:"数据导出"},{default:t(()=>[l(_,{onClick:N,type:"primary",plain:""},{default:t(()=>[l(r,null,{default:t(()=>[l(W)]),_:1}),e[28]||(e[28]=d(" 导出运动数据 ",-1))]),_:1,__:[28]}),e[29]||(e[29]=n("div",{class:"form-help"},"导出所有运动记录和统计数据",-1))]),_:1,__:[29]}),l(u,{label:"重置数据"},{default:t(()=>[l(_,{onClick:$,type:"danger",plain:""},{default:t(()=>[l(r,null,{default:t(()=>[l(j)]),_:1}),e[30]||(e[30]=d(" 重置所有数据 ",-1))]),_:1,__:[30]}),e[31]||(e[31]=n("div",{class:"form-help"},"清除所有运动记录和设置（不可恢复）",-1))]),_:1,__:[31]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),l(P,{modelValue:f.value,"onUpdate:modelValue":e[11]||(e[11]=a=>f.value=a),title:"确认重置",width:"400px",center:""},{footer:t(()=>[l(_,{onClick:e[10]||(e[10]=a=>f.value=!1)},{default:t(()=>e[34]||(e[34]=[d("取消",-1)])),_:1,__:[34]}),l(_,{type:"danger",onClick:I},{default:t(()=>e[35]||(e[35]=[d("确认重置",-1)])),_:1,__:[35]})]),default:t(()=>[n("div",De,[l(r,{class:"warning-icon"},{default:t(()=>[l(O)]),_:1}),e[32]||(e[32]=n("p",null,"此操作将清除所有运动记录和设置，且不可恢复。",-1)),e[33]||(e[33]=n("p",null,"确定要继续吗？",-1))])]),_:1},8,["modelValue"])])}}}),$e=z(He,[["__scopeId","data-v-cc01b0de"]]);export{$e as default};
