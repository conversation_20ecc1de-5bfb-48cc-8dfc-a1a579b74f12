{"rustc": 1842507548689473721, "features": "[\"app-hide\", \"app-show\", \"compression\", \"custom-protocol\", \"default\", \"dialog\", \"dialog-ask\", \"fs-exists\", \"fs-read-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"ico\", \"icon-ico\", \"icon-png\", \"infer\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"path-all\", \"png\", \"regex\", \"rfd\", \"shell-open\", \"shell-open-api\", \"sys-locale\", \"tauri-runtime-wry\", \"window-center\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-set-focus\", \"window-set-position\", \"window-set-size\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 16287072401622553093, "path": 2779529947099847214, "deps": [[40386456601120721, "percent_encoding", false, 18168946500566215783], [947818755262499932, "notify_rust", false, 1625095264514409514], [1260461579271933187, "serialize_to_javascript", false, 13184397625357724132], [1441306149310335789, "tempfile", false, 9199718854355050333], [3150220818285335163, "url", false, 2607936323075669519], [3722963349756955755, "once_cell", false, 3786254663879612], [3988549704697787137, "open", false, 17128224689989841540], [4381063397040571828, "webview2_com", false, 2109139913616277398], [4405182208873388884, "http", false, 8119963941407273293], [4450062412064442726, "dirs_next", false, 3761337855387268545], [4899080583175475170, "semver", false, 11231862191787307630], [5024769281214949041, "os_info", false, 6026902803910351130], [5099504066399492044, "rfd", false, 13746713302447260719], [5180608563399064494, "tauri_macros", false, 4996164974772365095], [5610773616282026064, "build_script_build", false, 16037378410713267459], [5986029879202738730, "log", false, 8837273349723292855], [6997837210367702832, "infer", false, 10003073876284108651], [7392050791754369441, "ico", false, 11179072387287296091], [7653476968652377684, "windows", false, 2672332482407547069], [8008191657135824715, "thiserror", false, 7441644336200258125], [8292277814562636972, "tauri_utils", false, 16259485879196958486], [8319709847752024821, "uuid", false, 7522972156232273135], [8569119365930580996, "serde_json", false, 11246661942346795939], [9451456094439810778, "regex", false, 13004480284770706601], [9623796893764309825, "ignore", false, 17794564311955155322], [9689903380558560274, "serde", false, 16541609886822019218], [9920160576179037441, "getrandom", false, 4997246292711259605], [10629569228670356391, "futures_util", false, 4995427890295641337], [11601763207901161556, "tar", false, 16935936515326105238], [11693073011723388840, "raw_window_handle", false, 3518983977318704076], [11989259058781683633, "dunce", false, 10411694973672158412], [12687914511023397207, "png", false, 14560483975447925532], [12944427623413450645, "tokio", false, 13881276530881947558], [12986574360607194341, "serde_repr", false, 3904129036574183055], [13208667028893622512, "rand", false, 5082811502463348362], [13625485746686963219, "anyhow", false, 4531216291603673053], [14162324460024849578, "tauri_runtime", false, 8922351376865114571], [14564311161534545801, "encoding_rs", false, 13677179028349758560], [14618885535728128396, "sys_locale", false, 14614946939859090229], [16228250612241359704, "tauri_runtime_wry", false, 15463286803000766027], [17155886227862585100, "glob", false, 14199550071656753871], [17278893514130263345, "state", false, 5881915025723330637], [17772299992546037086, "flate2", false, 5826462155461135588]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-4b4f8e8cfeb7ae63\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}