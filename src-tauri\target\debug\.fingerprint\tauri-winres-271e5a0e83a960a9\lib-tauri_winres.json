{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10555948925697328552, "profile": 2225463790103693989, "path": 6153130744623688991, "deps": [[1293861355733423614, "toml", false, 6046512513507291154], [11501392865286155686, "embed_resource", false, 5694125828982722476]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-271e5a0e83a960a9\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}