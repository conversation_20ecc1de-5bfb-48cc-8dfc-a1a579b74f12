import{_ as P}from"./_plugin-vue_export-helper-BiY_o3Ly.js";import{d as z,r as w,v as W,o as F,x as X,a as i,e as l,i as e,y as b,z as T,F as B,A as I,t as c,B as M,c as A,b as a,k as H,w as u,g as k,C as ve,D as pe,l as q,G as _e,H as me,j as D,E as he,h as fe,m as ye,n as ge,p as g,s as S,f as we}from"./index-C32AESHs.js";import{u as Ce}from"./settings-CmLFLaNK.js";import{u as ke}from"./exercise-CGbk4vEv.js";const be={class:"breathing-animation"},$e={class:"animation-container"},Se={key:0,class:"particles"},Te={class:"breathing-text"},Ee={class:"instruction-text"},Ae={key:0,class:"progress-indicator"},xe={class:"progress-dots"},De={class:"cycle-text"},Be=z({__name:"BreathingAnimation",props:{isActive:{type:Boolean,default:!1},cycleDuration:{default:8},showProgress:{type:Boolean,default:!1},totalCycles:{default:10},currentCycle:{default:0}},emits:["cycleComplete","phaseChange"],setup(E,{expose:h,emit:t}){const C=E,_=t,r=w(!0),d=w(null),y=()=>{d.value&&clearInterval(d.value);const n=C.cycleDuration*1e3/2;d.value=setInterval(()=>{r.value=!r.value,_("phaseChange",r.value?"inhale":"exhale"),r.value&&_("cycleComplete")},n)},s=()=>{d.value&&(clearInterval(d.value),d.value=null),r.value=!0};return W(()=>C.isActive,n=>{n?y():s()}),F(()=>{C.isActive&&y()}),X(()=>{s()}),h({startAnimation:y,stopAnimation:s}),(n,f)=>(l(),i("div",be,[e("div",$e,[e("div",{class:T(["breathing-circle main-circle",{inhale:r.value,exhale:!r.value,active:n.isActive}])},f[0]||(f[0]=[e("div",{class:"inner-circle"},null,-1),e("div",{class:"center-dot"},null,-1)]),2),e("div",{class:T(["outer-ring",{active:n.isActive}])},null,2),n.isActive?(l(),i("div",Se,[(l(),i(B,null,I(8,v=>e("div",{key:v,class:"particle",style:M({"--delay":v*.5+"s"})},null,4)),64))])):b("",!0)]),e("div",Te,[e("div",{class:T(["phase-text",{inhale:r.value,exhale:!r.value}])},c(r.value?"吸气 - 收缩":"呼气 - 放松"),3),e("div",Ee,c(r.value?"慢慢收紧肛门肌肉":"缓缓放松肌肉"),1)]),n.showProgress?(l(),i("div",Ae,[e("div",xe,[(l(!0),i(B,null,I(n.totalCycles,v=>(l(),i("div",{key:v,class:T(["progress-dot",{completed:v<=n.currentCycle}])},null,2))),128))]),e("div",De,c(n.currentCycle)+"/"+c(n.totalCycles)+" 次 ",1)])):b("",!0)]))}}),Ie=P(Be,[["__scopeId","data-v-7eaef795"]]),Re={class:"progress-animation"},Me={class:"circular-progress"},Pe=["width","height"],ze=["cx","cy","r","stroke-width"],Ve=["cx","cy","r","stroke-width","stroke-dasharray","stroke-dashoffset"],Le={class:"progress-content"},Ne={class:"progress-value"},We={class:"progress-label"},Fe={key:0,class:"linear-progress"},He={class:"linear-bg"},je={class:"linear-text"},Ge={key:1,class:"step-indicators"},Ue={class:"step-number"},Xe={key:0,class:"step-connector"},qe={key:2,class:"success-animation"},Je={class:"success-circle"},Ke={class:"success-text"},Oe=z({__name:"ProgressAnimation",props:{percentage:{default:0},size:{default:120},strokeWidth:{default:8},color:{default:"#67c23a"},backgroundColor:{default:"#e4e7ed"},label:{default:"进度"},showLinear:{type:Boolean,default:!1},showSteps:{type:Boolean,default:!1},showSuccess:{type:Boolean,default:!1},currentStep:{default:0},totalSteps:{default:10},stepLabel:{default:"步骤"},successText:{default:"完成！"},animationDuration:{default:.5},isAnimating:{type:Boolean,default:!1}},emits:["complete","stepChange"],setup(E,{emit:h}){ve(s=>({"91adc452":s.backgroundColor,"6f4ad0e5":s.color}));const t=E,C=h,_=A(()=>t.size/2),r=A(()=>(t.size-t.strokeWidth)/2),d=A(()=>2*Math.PI*r.value),y=A(()=>{const s=Math.min(Math.max(t.percentage,0),100);return d.value-s/100*d.value});return W(()=>t.percentage,(s,n)=>{s>=100&&n<100&&setTimeout(()=>{C("complete")},t.animationDuration*1e3)}),W(()=>t.currentStep,(s,n)=>{s!==n&&C("stepChange",s)}),(s,n)=>{const f=k("Check"),v=H;return l(),i("div",Re,[e("div",Me,[(l(),i("svg",{class:"progress-svg",width:s.size,height:s.size},[e("circle",{class:"progress-bg",cx:_.value,cy:_.value,r:r.value,fill:"none","stroke-width":s.strokeWidth},null,8,ze),e("circle",{class:"progress-bar",cx:_.value,cy:_.value,r:r.value,fill:"none","stroke-width":s.strokeWidth,"stroke-dasharray":d.value,"stroke-dashoffset":y.value,style:M({"--duration":s.animationDuration+"s"})},null,12,Ve)],8,Pe)),e("div",Le,[e("div",Ne,c(Math.round(s.percentage))+"%",1),e("div",We,c(s.label),1)])]),s.showLinear?(l(),i("div",Fe,[e("div",He,[e("div",{class:"linear-bar",style:M({width:s.percentage+"%","--duration":s.animationDuration+"s"})},null,4)]),e("div",je,c(s.currentStep)+"/"+c(s.totalSteps)+" "+c(s.stepLabel),1)])):b("",!0),s.showSteps?(l(),i("div",Ge,[(l(!0),i(B,null,I(s.totalSteps,p=>(l(),i("div",{key:p,class:T(["step-indicator",{completed:p<=s.currentStep,active:p===s.currentStep+1,pulse:p===s.currentStep+1&&s.isAnimating}])},[e("div",Ue,c(p),1),p<s.totalSteps?(l(),i("div",Xe)):b("",!0)],2))),128))])):b("",!0),s.showSuccess?(l(),i("div",qe,[e("div",Je,[a(v,{class:"success-icon"},{default:u(()=>[a(f)]),_:1})]),e("div",Ke,c(s.successText),1)])):b("",!0)])}}}),Qe=P(Oe,[["__scopeId","data-v-7a9c0dbb"]]),Ye={key:0,class:"reward-animation"},Ze={class:"confetti-container"},es={class:"reward-icon-container"},ss={class:"reward-text"},ts={class:"reward-title"},as={class:"reward-message"},os={key:0,class:"reward-points"},ns={key:0,class:"reward-stats"},ls={class:"stat-value"},is={class:"stat-label"},rs=z({__name:"RewardAnimation",props:{visible:{type:Boolean},rewardType:{default:"success"},title:{default:"恭喜！"},message:{default:"你完成了一次运动！"},points:{},stats:{},closeText:{default:"继续"},autoClose:{type:Boolean,default:!1},autoCloseDelay:{default:3e3}},emits:["close"],setup(E,{emit:h}){const t=E,C=h,_=w(!1),r=A(()=>({success:"Check",achievement:"Trophy",milestone:"Medal",streak:"Star"})[t.rewardType]||"Check"),d=s=>{const n=["#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#feca57","#ff9ff3","#54a0ff"],f=n[s%n.length],v=Math.random()*3,p=2+Math.random()*2,x=Math.random()*100,V=Math.random()*360;return{"--color":f,"--delay":v+"s","--duration":p+"s","--x":x+"%","--rotation":V+"deg"}},y=()=>{_.value=!1,setTimeout(()=>{C("close")},300)};return F(async()=>{t.visible&&(await pe(),setTimeout(()=>{_.value=!0},100),t.autoClose&&setTimeout(()=>{y()},t.autoCloseDelay))}),(s,n)=>{const f=H,v=q;return s.visible?(l(),i("div",Ye,[e("div",{class:"reward-overlay",onClick:y}),e("div",{class:T(["reward-content",{show:_.value}])},[e("div",Ze,[(l(),i(B,null,I(50,p=>e("div",{key:p,class:"confetti",style:M(d(p))},null,4)),64))]),e("div",es,[e("div",{class:T(["reward-icon",s.rewardType])},[a(f,null,{default:u(()=>[(l(),_e(me(r.value)))]),_:1})],2),n[0]||(n[0]=e("div",{class:"reward-glow"},null,-1))]),e("div",ss,[e("h2",ts,c(s.title),1),e("p",as,c(s.message),1),s.points?(l(),i("div",os," +"+c(s.points)+" 积分 ",1)):b("",!0)]),s.stats?(l(),i("div",ns,[(l(!0),i(B,null,I(s.stats,(p,x)=>(l(),i("div",{class:"stat-item",key:x},[e("div",ls,c(p.value),1),e("div",is,c(p.label),1)]))),128))])):b("",!0),a(v,{type:"primary",size:"large",class:"reward-close-btn",onClick:y},{default:u(()=>[D(c(s.closeText),1)]),_:1})],2)])):b("",!0)}}}),cs=P(rs,[["__scopeId","data-v-c2d54d10"]]),us={class:"exercise-view"},ds={class:"header-content"},vs={key:0,class:"exercise-ready"},ps={class:"exercise-info"},_s={class:"exercise-params"},ms={class:"param-item"},hs={class:"param-item"},fs={key:1,class:"exercise-active"},ys={class:"time-display"},gs={class:"exercise-controls"},ws={class:"today-stats"},Cs={class:"stat-item"},ks={class:"stat-info"},bs={class:"stat-value"},$s={class:"stat-item"},Ss={class:"stat-info"},Ts={class:"stat-value"},Es=z({__name:"ExerciseView",setup(E){we();const h=Ce(),t=ke(),C=w(0),_=w(!0),r=w(null),d=w(null),y=w(!1),s=w("success"),n=w("恭喜！"),f=w("你完成了一次运动！"),v=w(0),p=w(null),x=A(()=>m=>{const o=Math.floor(m/60),N=m%60;return`${o.toString().padStart(2,"0")}:${N.toString().padStart(2,"0")}`}),V=async()=>{try{await t.beginExercise(h.settings.exerciseDuration,h.settings.repetitions)?(ee(),se(),S.success("运动开始！")):S.error("开始运动失败")}catch{S.error("开始运动时出错")}},J=async()=>{try{await t.finishExercise(!1),L(),S.info("运动已停止")}catch{S.error("停止运动时出错")}},j=()=>{t.incrementRepetition(),t.currentRepetition>=h.settings.repetitions&&G()},G=async()=>{try{await t.finishExercise(!0),L(),Y()}catch{S.error("完成运动时出错")}},K=m=>{_.value=m==="inhale"},O=()=>{G()},Q=m=>{console.log("步骤变化:",m)},Y=()=>{const m=t.stats.todayCount+1,o=t.stats.streakDays;o>=30?(s.value="milestone",n.value="里程碑达成！",f.value="连续运动30天，你是真正的健康达人！",v.value=100):o>=7?(s.value="streak",n.value="连续记录！",f.value=`连续运动${o}天，坚持就是胜利！`,v.value=50):m>=10?(s.value="achievement",n.value="今日达人！",f.value="今天已完成10次运动，表现优秀！",v.value=30):(s.value="success",n.value="运动完成！",f.value="又完成了一次健康运动，继续保持！",v.value=10),p.value={today:{value:m,label:"今日次数"},streak:{value:o,label:"连续天数"},total:{value:t.stats.totalCount+1,label:"总计次数"}},y.value=!0},Z=()=>{y.value=!1,S.success("恭喜！运动完成！")},ee=()=>{r.value=setInterval(()=>{_.value=!_.value},4e3)},se=()=>{d.value=setInterval(()=>{C.value++},1e3)},L=()=>{r.value&&(clearInterval(r.value),r.value=null),d.value&&(clearInterval(d.value),d.value=null),C.value=0,_.value=!0};return F(async()=>{await Promise.all([h.loadSettings(),t.loadStats()])}),X(()=>{L()}),(m,o)=>{const N=k("ArrowLeft"),$=H,R=q,te=fe,ae=k("Timer"),oe=k("Refresh"),ne=k("VideoPlay"),le=k("Check"),ie=k("Close"),U=ge,re=k("Trophy"),ce=k("Clock"),ue=ye,de=he;return l(),i("div",us,[a(de,null,{default:u(()=>[a(te,{class:"header"},{default:u(()=>[e("div",ds,[a(R,{onClick:o[0]||(o[0]=As=>m.$router.back()),type:"text",class:"back-btn"},{default:u(()=>[a($,null,{default:u(()=>[a(N)]),_:1}),o[1]||(o[1]=D(" 返回 ",-1))]),_:1,__:[1]}),o[2]||(o[2]=e("h2",null,"运动指导",-1)),o[3]||(o[3]=e("div",null,null,-1))])]),_:1}),a(ue,{class:"main-content"},{default:u(()=>[a(U,{class:"exercise-card"},{default:u(()=>[g(t).isExercising?(l(),i("div",fs,[a(Ie,{"is-active":g(t).isExercising,"cycle-duration":8,"show-progress":!0,"total-cycles":g(h).settings.repetitions,"current-cycle":g(t).currentRepetition,onCycleComplete:j,onPhaseChange:K},null,8,["is-active","total-cycles","current-cycle"]),a(Qe,{percentage:g(t).exerciseProgress,"current-step":g(t).currentRepetition,"total-steps":g(h).settings.repetitions,"show-linear":!0,"show-steps":!0,"step-label":"次","is-animating":g(t).isExercising,onComplete:O,onStepChange:Q},null,8,["percentage","current-step","total-steps","is-animating"]),e("div",ys,[e("span",null,c(x.value(C.value)),1)]),e("div",gs,[a(R,{onClick:j,type:"primary",size:"large"},{default:u(()=>[a($,null,{default:u(()=>[a(le)]),_:1}),o[7]||(o[7]=D(" 完成一次 ",-1))]),_:1,__:[7]}),a(R,{onClick:J,type:"danger",plain:""},{default:u(()=>[a($,null,{default:u(()=>[a(ie)]),_:1}),o[8]||(o[8]=D(" 停止运动 ",-1))]),_:1,__:[8]})])])):(l(),i("div",vs,[e("div",ps,[o[4]||(o[4]=e("h3",null,"准备开始提肛运动",-1)),e("div",_s,[e("div",ms,[a($,null,{default:u(()=>[a(ae)]),_:1}),e("span",null,"持续时间: "+c(g(h).settings.exerciseDuration)+"秒",1)]),e("div",hs,[a($,null,{default:u(()=>[a(oe)]),_:1}),e("span",null,"重复次数: "+c(g(h).settings.repetitions)+"次",1)])])]),o[6]||(o[6]=e("div",{class:"exercise-guide"},[e("h4",null,"动作要领"),e("ol",{class:"guide-steps"},[e("li",null,"坐直或站立，保持背部挺直"),e("li",null,'收紧腹部，想象夹断"粑粑"的感觉'),e("li",null,"吸气时收缩肛门肌肉，坚持3-5秒"),e("li",null,"呼气时放松肌肉，休息3-5秒"),e("li",null,"重复以上动作")])],-1)),a(R,{type:"primary",size:"large",class:"start-btn",onClick:V,loading:g(t).loading},{default:u(()=>[a($,null,{default:u(()=>[a(ne)]),_:1}),o[5]||(o[5]=D(" 开始运动 ",-1))]),_:1,__:[5]},8,["loading"])]))]),_:1}),a(U,{class:"stats-card"},{header:u(()=>o[9]||(o[9]=[e("span",null,"今日统计",-1)])),default:u(()=>[e("div",ws,[e("div",Cs,[a($,{class:"stat-icon"},{default:u(()=>[a(re)]),_:1}),e("div",ks,[e("div",bs,c(g(t).stats.todayCount),1),o[10]||(o[10]=e("div",{class:"stat-label"},"完成次数",-1))])]),e("div",$s,[a($,{class:"stat-icon"},{default:u(()=>[a(ce)]),_:1}),e("div",Ss,[e("div",Ts,c(g(t).stats.streakDays),1),o[11]||(o[11]=e("div",{class:"stat-label"},"连续天数",-1))])])])]),_:1})]),_:1})]),_:1}),a(cs,{visible:y.value,"reward-type":s.value,title:n.value,message:f.value,points:v.value,stats:p.value||void 0,onClose:Z},null,8,["visible","reward-type","title","message","points","stats"])])}}}),Rs=P(Es,[["__scopeId","data-v-85c8bc23"]]);export{Rs as default};
