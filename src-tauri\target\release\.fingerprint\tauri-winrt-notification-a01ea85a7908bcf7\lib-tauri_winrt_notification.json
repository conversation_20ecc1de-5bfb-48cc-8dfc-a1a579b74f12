{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 2040997289075261528, "path": 10434116139574476539, "deps": [[1462335029370885857, "quick_xml", false, 2110248978360178217], [3334271191048661305, "windows_version", false, 15232365920762273844], [10806645703491011684, "thiserror", false, 10065494875784585067], [14585479307175734061, "windows", false, 15262311300149109601]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winrt-notification-a01ea85a7908bcf7\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}