import{P as x,r as s,c as f}from"./index-C32AESHs.js";import{a as E,b as g,s as p,c as S}from"./_plugin-vue_export-helper-BiY_o3Ly.js";const T=x("exercise",()=>{const c=s({todayCount:0,weekCount:0,monthCount:0,totalCount:0,streakDays:0}),t=s(null),v=s({enabled:!1,nextReminder:"",interval:30}),u=s(!1),o=s(null),n=s(0),a=s(!1),l=s(null),m=f(()=>!u.value&&!a.value),d=f(()=>!t.value||!u.value?0:n.value/t.value.repetitions*100);return{stats:c,currentExercise:t,reminderStatus:v,isExercising:u,currentRepetition:n,loading:a,error:l,canStartExercise:m,exerciseProgress:d,loadStats:async()=>{try{a.value=!0,l.value=null;const e=await E();e.data&&(c.value=e.data)}catch(e){l.value=e instanceof Error?e.message:"加载统计数据失败",console.error("加载统计数据失败:",e)}finally{a.value=!1}},loadReminderStatus:async()=>{try{const e=await g();e.data&&(v.value=e.data)}catch(e){console.error("加载提醒状态失败:",e)}},beginExercise:async(e,r)=>{try{a.value=!0,l.value=null;const i=await p();if(i.success)return t.value={id:Date.now().toString(),startTime:new Date().toISOString(),endTime:"",duration:e,repetitions:r,completed:!1},u.value=!0,n.value=0,!0;throw new Error(i.message||"开始运动失败")}catch(i){return l.value=i instanceof Error?i.message:"开始运动失败",console.error("开始运动失败:",i),!1}finally{a.value=!1}},finishExercise:async(e=!0)=>{try{if(!t.value)return!1;a.value=!0,l.value=null;const r=await S();if(r.success)return t.value.endTime=new Date().toISOString(),t.value.completed=e,e&&(c.value.todayCount++,c.value.totalCount++),u.value=!1,t.value=null,n.value=0,o.value&&(clearInterval(o.value),o.value=null),!0;throw new Error(r.message||"完成运动失败")}catch(r){return l.value=r instanceof Error?r.message:"完成运动失败",console.error("完成运动失败:",r),!1}finally{a.value=!1}},incrementRepetition:()=>{t.value&&n.value<t.value.repetitions&&n.value++},resetExercise:()=>{u.value=!1,t.value=null,n.value=0,o.value&&(clearInterval(o.value),o.value=null)}}});export{T as u};
