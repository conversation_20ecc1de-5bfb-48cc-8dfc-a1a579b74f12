import{P as m,r as o,c as v}from"./index-C32AESHs.js";import{g as f,u as p}from"./_plugin-vue_export-helper-BiY_o3Ly.js";const k=m("settings",()=>{const t=o({reminderInterval:30,exerciseDuration:5,repetitions:10,enableSound:!0,enableNotifications:!0,theme:"light",workingHours:{start:"09:00",end:"18:00",enabled:!0}}),n=o(!1),s=o(null),i=v(()=>{if(!t.value.workingHours.enabled)return!0;const e=new Date,r=e.getHours()*60+e.getMinutes(),[a,u]=t.value.workingHours.start.split(":").map(Number),[l,c]=t.value.workingHours.end.split(":").map(Number),g=a*60+u,d=l*60+c;return r>=g&&r<=d});return{settings:t,loading:n,error:s,isWorkingTime:i,loadSettings:async()=>{try{n.value=!0,s.value=null;const e=await f();e.data&&(t.value={...t.value,...e.data})}catch(e){s.value=e instanceof Error?e.message:"加载设置失败",console.error("加载设置失败:",e)}finally{n.value=!1}},saveSettings:async e=>{try{n.value=!0,s.value=null;const r={...t.value,...e},a=await p(r);if(a.success)return t.value=r,!0;throw new Error(a.message||"保存设置失败")}catch(r){return s.value=r instanceof Error?r.message:"保存设置失败",console.error("保存设置失败:",r),!1}finally{n.value=!1}},resetSettings:()=>{t.value={reminderInterval:30,exerciseDuration:5,repetitions:10,enableSound:!0,enableNotifications:!0,theme:"light",workingHours:{start:"09:00",end:"18:00",enabled:!0}}}}});export{k as u};
