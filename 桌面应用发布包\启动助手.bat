@echo off
chcp 65001 >nul
title 提肛小助手启动助手

echo.
echo ╔══════════════════════════════════════╗
echo ║          提肛小助手启动助手            ║
echo ╚══════════════════════════════════════╝
echo.

echo [信息] 正在启动提肛小助手桌面应用...
echo.

:: 检查exe文件是否存在
if not exist "提肛小助手.exe" (
    echo ❌ 错误：找不到 提肛小助手.exe 文件
    echo 请确保此批处理文件与exe文件在同一目录
    pause
    exit /b 1
)

:: 启动桌面应用
echo ✅ 启动桌面应用...
start "" "提肛小助手.exe"

:: 等待应用启动
timeout /t 2 >nul

echo.
echo 🎉 提肛小助手已启动！
echo.
echo 📝 使用提示：
echo    • 首次运行可能需要Windows安全确认
echo    • 应用支持系统托盘最小化
echo    • 可在设置中调整运动参数和提醒
echo.
echo 如果应用未正常启动，请：
echo    1. 检查Windows版本（需要Win10/11）
echo    2. 安装Microsoft Edge WebView2
echo    3. 以管理员身份运行
echo.

pause
