#!/usr/bin/env node

const http = require('http');

console.log('🧪 测试提肛小助手"开始运动"功能\n');

// 测试配置
const API_BASE = 'http://127.0.0.1:8000/api/v1';
const tests = [
    {
        name: '健康检查',
        method: 'GET',
        path: '/health',
        baseUrl: 'http://127.0.0.1:8000'
    },
    {
        name: '获取用户设置',
        method: 'GET',
        path: '/settings'
    },
    {
        name: '开始运动 (核心功能)',
        method: 'POST',
        path: '/exercise/start'
    },
    {
        name: '完成运动',
        method: 'POST',
        path: '/exercise/complete'
    },
    {
        name: '获取统计数据',
        method: 'GET',
        path: '/stats'
    }
];

// 发送HTTP请求
function makeRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        data: jsonData
                    });
                } catch (e) {
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        data: body
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 运行单个测试
async function runTest(test) {
    const url = (test.baseUrl || API_BASE) + test.path;
    
    try {
        console.log(`📋 测试: ${test.name}`);
        console.log(`   请求: ${test.method} ${url}`);
        
        const result = await makeRequest(url, test.method);
        
        if (result.success) {
            console.log(`   ✅ 成功 (${result.status})`);
            if (test.name.includes('开始运动')) {
                console.log(`   🎯 核心功能正常！响应: ${JSON.stringify(result.data)}`);
            } else {
                console.log(`   📄 响应: ${JSON.stringify(result.data)}`);
            }
        } else {
            console.log(`   ❌ 失败 (${result.status})`);
            console.log(`   📄 响应: ${JSON.stringify(result.data)}`);
        }
        
        return result.success;
    } catch (error) {
        console.log(`   ❌ 错误: ${error.message}`);
        return false;
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('开始API功能测试...\n');
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        const success = await runTest(test);
        if (success) passedTests++;
        console.log(''); // 空行分隔
        
        // 在测试之间稍作延迟
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('📊 测试结果汇总:');
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过测试: ${passedTests}`);
    console.log(`   失败测试: ${totalTests - passedTests}`);
    console.log(`   成功率: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！"开始运动"功能正常工作！');
    } else {
        console.log('\n⚠️  部分测试失败，请检查后端服务状态');
    }
    
    // 特别检查核心功能
    const exerciseTest = tests.find(t => t.name.includes('开始运动'));
    if (exerciseTest) {
        console.log('\n🔍 核心功能检查:');
        const success = await runTest(exerciseTest);
        if (success) {
            console.log('✅ "开始运动"功能修复成功！');
        } else {
            console.log('❌ "开始运动"功能仍有问题');
        }
    }
}

// 启动测试
console.log('等待2秒确保服务启动完成...');
setTimeout(() => {
    runAllTests().catch(console.error);
}, 2000);
