use std::fs;
use std::path::Path;

fn main() {
    // 复制后端文件到资源目录
    copy_backend_files();

    tauri_build::build()
}

fn copy_backend_files() {
    let backend_src = Path::new("../backend");
    let backend_dest = Path::new("target/backend");

    if backend_src.exists() {
        // 创建目标目录
        if let Err(e) = fs::create_dir_all(backend_dest) {
            println!("cargo:warning=创建后端目录失败: {}", e);
            return;
        }

        // 复制后端文件
        if let Err(e) = copy_dir_all(backend_src, backend_dest) {
            println!("cargo:warning=复制后端文件失败: {}", e);
        } else {
            println!("cargo:warning=后端文件复制成功");
        }
    } else {
        println!("cargo:warning=后端源目录不存在: {:?}", backend_src);
    }
}

fn copy_dir_all(src: &Path, dst: &Path) -> std::io::Result<()> {
    if !dst.exists() {
        fs::create_dir_all(dst)?;
    }

    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        let src_path = entry.path();
        let dst_path = dst.join(entry.file_name());

        if ty.is_dir() {
            copy_dir_all(&src_path, &dst_path)?;
        } else {
            fs::copy(&src_path, &dst_path)?;
        }
    }

    Ok(())
}
