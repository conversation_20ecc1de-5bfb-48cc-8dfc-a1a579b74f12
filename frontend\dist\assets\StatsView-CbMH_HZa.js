import{_ as j}from"./_plugin-vue_export-helper-BiY_o3Ly.js";/* empty css                       */import{d as P,r as g,c as Q,o as U,a as f,b as t,w as o,E as q,s as E,e as u,h as J,i as s,l as K,j as k,k as O,g as d,m as W,n as X,t as c,p as _,N as Y,Q as Z,F as tt,A as st,z as et,G as x,y as b,H as ot,f as at}from"./index-C32AESHs.js";import{u as nt}from"./exercise-CGbk4vEv.js";const lt={class:"stats-view"},rt={class:"header-content"},dt={class:"overview-stats"},ct={class:"stat-content"},it={class:"stat-info"},ut={class:"stat-value"},_t={class:"stat-content"},ft={class:"stat-info"},pt={class:"stat-value"},mt={class:"stat-content"},vt={class:"stat-info"},kt={class:"stat-value"},Ct={class:"stat-content"},ht={class:"stat-info"},yt={class:"stat-value"},wt={class:"card-header"},gt={class:"streak-content"},Et={class:"streak-number"},xt={class:"streak-encouragement"},bt={class:"card-header"},Tt={class:"chart-container"},Dt={class:"chart-placeholder"},St={class:"chart-desc"},Mt={class:"card-header"},Vt={class:"achievements"},Rt={class:"achievement-info"},Bt={class:"achievement-title"},Lt={class:"achievement-desc"},At={key:0,class:"achievement-progress"},Nt={class:"card-header"},$t={class:"recent-records"},zt={key:0,class:"last-exercise"},Ht={key:1,class:"no-records"},Ft=P({__name:"StatsView",setup(Gt){at();const n=nt(),m=g("week"),C=g([{id:1,title:"初学者",description:"完成第一次运动",icon:"Trophy",target:1,current:n.stats.totalCount,unlocked:n.stats.totalCount>=1},{id:2,title:"坚持者",description:"连续运动7天",icon:"Medal",target:7,current:n.stats.streakDays,unlocked:n.stats.streakDays>=7},{id:3,title:"百次达人",description:"累计完成100次运动",icon:"Star",target:100,current:n.stats.totalCount,unlocked:n.stats.totalCount>=100},{id:4,title:"月度冠军",description:"单月完成50次运动",icon:"Crown",target:50,current:n.stats.monthCount,unlocked:n.stats.monthCount>=50}]),T=Q(()=>{if(!n.stats.lastExerciseTime)return"";const a=new Date(n.stats.lastExerciseTime),v=new Date().getTime()-a.getTime(),l=Math.floor(v/(1e3*60*60)),p=Math.floor(l/24);return p>0?`${p}天前`:l>0?`${l}小时前`:"刚刚"}),D=a=>a===0?"开始你的健康之旅吧！":a<7?"很好的开始，继续保持！":a<30?"习惯正在养成，加油！":a<100?"你已经是运动达人了！":"健康大师，令人敬佩！",h=async()=>{try{await n.loadStats(),C.value.forEach(a=>{a.id===1||a.id===3?(a.current=n.stats.totalCount,a.unlocked=a.current>=a.target):a.id===2?(a.current=n.stats.streakDays,a.unlocked=a.current>=a.target):a.id===4&&(a.current=n.stats.monthCount,a.unlocked=a.current>=a.target)}),E.success("数据已刷新")}catch{E.error("刷新数据失败")}};return U(async()=>{await h()}),(a,e)=>{const v=d("ArrowLeft"),l=O,p=K,S=d("Refresh"),M=J,V=d("Calendar"),i=X,R=d("DataLine"),y=d("TrendCharts"),B=d("Trophy"),L=d("Medal"),A=d("DataAnalysis"),w=Z,N=Y,$=d("Star"),z=d("Check"),H=d("Clock"),F=d("DocumentRemove"),G=W,I=q;return u(),f("div",lt,[t(I,null,{default:o(()=>[t(M,{class:"header"},{default:o(()=>[s("div",rt,[t(p,{onClick:e[0]||(e[0]=r=>a.$router.back()),type:"text",class:"back-btn"},{default:o(()=>[t(l,null,{default:o(()=>[t(v)]),_:1}),e[2]||(e[2]=k(" 返回 ",-1))]),_:1,__:[2]}),e[3]||(e[3]=s("h2",null,"统计数据",-1)),t(p,{onClick:h,type:"text",class:"refresh-btn"},{default:o(()=>[t(l,null,{default:o(()=>[t(S)]),_:1})]),_:1})])]),_:1}),t(G,{class:"main-content"},{default:o(()=>[s("div",dt,[t(i,{class:"stat-card today"},{default:o(()=>[s("div",ct,[t(l,{class:"stat-icon"},{default:o(()=>[t(V)]),_:1}),s("div",it,[s("div",ut,c(_(n).stats.todayCount),1),e[4]||(e[4]=s("div",{class:"stat-label"},"今日次数",-1))])])]),_:1}),t(i,{class:"stat-card week"},{default:o(()=>[s("div",_t,[t(l,{class:"stat-icon"},{default:o(()=>[t(R)]),_:1}),s("div",ft,[s("div",pt,c(_(n).stats.weekCount),1),e[5]||(e[5]=s("div",{class:"stat-label"},"本周次数",-1))])])]),_:1}),t(i,{class:"stat-card month"},{default:o(()=>[s("div",mt,[t(l,{class:"stat-icon"},{default:o(()=>[t(y)]),_:1}),s("div",vt,[s("div",kt,c(_(n).stats.monthCount),1),e[6]||(e[6]=s("div",{class:"stat-label"},"本月次数",-1))])])]),_:1}),t(i,{class:"stat-card total"},{default:o(()=>[s("div",Ct,[t(l,{class:"stat-icon"},{default:o(()=>[t(B)]),_:1}),s("div",ht,[s("div",yt,c(_(n).stats.totalCount),1),e[7]||(e[7]=s("div",{class:"stat-label"},"总计次数",-1))])])]),_:1})]),t(i,{class:"streak-card"},{header:o(()=>[s("div",wt,[t(l,null,{default:o(()=>[t(L)]),_:1}),e[8]||(e[8]=s("span",null,"连续记录",-1))])]),default:o(()=>[s("div",gt,[s("div",Et,c(_(n).stats.streakDays),1),e[9]||(e[9]=s("div",{class:"streak-text"},"连续运动天数",-1)),s("div",xt,c(D(_(n).stats.streakDays)),1)])]),_:1}),t(i,{class:"chart-card"},{header:o(()=>[s("div",bt,[t(l,null,{default:o(()=>[t(A)]),_:1}),e[12]||(e[12]=s("span",null,"运动趋势",-1)),t(N,{modelValue:m.value,"onUpdate:modelValue":e[1]||(e[1]=r=>m.value=r),size:"small"},{default:o(()=>[t(w,{label:"week"},{default:o(()=>e[10]||(e[10]=[k("本周",-1)])),_:1,__:[10]}),t(w,{label:"month"},{default:o(()=>e[11]||(e[11]=[k("本月",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])])]),default:o(()=>[s("div",Tt,[s("div",Dt,[t(l,{class:"chart-icon"},{default:o(()=>[t(y)]),_:1}),e[13]||(e[13]=s("p",null,"图表功能开发中...",-1)),s("p",St,"将显示"+c(m.value==="week"?"本周":"本月")+"的运动趋势",1)])])]),_:1}),t(i,{class:"achievement-card"},{header:o(()=>[s("div",Mt,[t(l,null,{default:o(()=>[t($)]),_:1}),e[14]||(e[14]=s("span",null,"成就系统",-1))])]),default:o(()=>[s("div",Vt,[(u(!0),f(tt,null,st(C.value,r=>(u(),f("div",{key:r.id,class:et(["achievement-item",{unlocked:r.unlocked}])},[t(l,{class:"achievement-icon"},{default:o(()=>[(u(),x(ot(r.icon)))]),_:2},1024),s("div",Rt,[s("div",Bt,c(r.title),1),s("div",Lt,c(r.description),1),r.unlocked?b("",!0):(u(),f("div",At,c(r.current)+"/"+c(r.target),1))]),r.unlocked?(u(),x(l,{key:0,class:"achievement-check"},{default:o(()=>[t(z)]),_:1})):b("",!0)],2))),128))])]),_:1}),t(i,{class:"recent-card"},{header:o(()=>[s("div",Nt,[t(l,null,{default:o(()=>[t(H)]),_:1}),e[15]||(e[15]=s("span",null,"最近记录",-1))])]),default:o(()=>[s("div",$t,[_(n).stats.lastExerciseTime?(u(),f("div",zt,[s("p",null,"最后运动时间："+c(T.value),1)])):(u(),f("div",Ht,[t(l,null,{default:o(()=>[t(F)]),_:1}),e[16]||(e[16]=s("p",null,"暂无运动记录",-1))]))])]),_:1})]),_:1})]),_:1})])}}}),Ut=j(Ft,[["__scopeId","data-v-75c9d475"]]);export{Ut as default};
