{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 6413393700164640249, "deps": [[2713742371683562785, "syn", false, 12710811965960026962], [3060637413840920116, "proc_macro2", false, 8406240879912885391], [8292277814562636972, "tauri_utils", false, 967489192104631831], [13077543566650298139, "heck", false, 17114546617626299270], [17492769205600034078, "tauri_codegen", false, 14979063009489118622], [17990358020177143287, "quote", false, 7647860114736051552]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-11b62d160b358707\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}