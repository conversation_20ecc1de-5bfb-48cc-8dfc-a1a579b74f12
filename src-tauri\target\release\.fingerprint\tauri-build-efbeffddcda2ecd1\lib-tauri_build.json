{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 13390825267576250845, "path": 3810753131390094618, "deps": [[4450062412064442726, "dirs_next", false, 5587001682211753291], [4899080583175475170, "semver", false, 10474756973477593717], [7468248713591957673, "cargo_toml", false, 2249649132464047495], [8292277814562636972, "tauri_utils", false, 16465916010854080396], [8569119365930580996, "serde_json", false, 4380246218091041972], [9689903380558560274, "serde", false, 1703231738526593199], [10301936376833819828, "json_patch", false, 13928870146076332136], [13077543566650298139, "heck", false, 3748740983335078662], [13625485746686963219, "anyhow", false, 7059862865587867860], [14189313126492979171, "tauri_winres", false, 3716356694066265400], [15622660310229662834, "walkdir", false, 11231307626965393364]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-efbeffddcda2ecd1\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}