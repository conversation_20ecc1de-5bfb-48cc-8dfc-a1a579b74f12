{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 1497995260721082616, "deps": [[4381063397040571828, "webview2_com", false, 1518854012022457691], [7653476968652377684, "windows", false, 11044908766836782643], [8292277814562636972, "tauri_utils", false, 2740083403207549112], [8319709847752024821, "uuid", false, 6814420306548153876], [8391357152270261188, "wry", false, 17870688967209755444], [11693073011723388840, "raw_window_handle", false, 14277391599310659604], [13208667028893622512, "rand", false, 7376770358957140781], [14162324460024849578, "tauri_runtime", false, 16711917615557223865], [16228250612241359704, "build_script_build", false, 3952907610622358473]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-1b69b9c3cea5f862\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}