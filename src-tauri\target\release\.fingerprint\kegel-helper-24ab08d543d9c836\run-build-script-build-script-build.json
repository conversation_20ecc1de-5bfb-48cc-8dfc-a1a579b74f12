{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8207884286030937749, "build_script_build", false, 9889278534651650529]], "local": [{"RerunIfChanged": {"output": "release\\build\\kegel-helper-24ab08d543d9c836\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}